import { supabase } from '@/lib/supabase';
import { RealTimeUser, RealTimeMessage } from '@/types/boss';

export interface RoomData {
  id: string;
  host_id: string;
  host_name: string;
  password_hash?: string;
  has_password: boolean;
  is_active: boolean;
  created_at: string;
  updated_at: string;
  last_activity: string;
  users?: RealTimeUser[];
}

export interface RoomMessage {
  id: string;
  room_id: string;
  user_id: string;
  user_name: string;
  message_type: string;
  message_data: any;
  created_at: string;
}

// Simple password hashing (for demo purposes - use proper hashing in production)
const hashPassword = (password: string): string => {
  return btoa(password + 'l2m-salt');
};

const verifyPassword = (password: string, hash: string): boolean => {
  return hashPassword(password) === hash;
};

export class SupabaseService {
  // Create a new room
  async createRoom(roomData: {
    id: string;
    host_id: string;
    host_name: string;
    password?: string;
  }): Promise<RoomData> {
    const { data, error } = await supabase
      .from('rooms')
      .insert({
        id: roomData.id,
        host_id: roomData.host_id,
        host_name: roomData.host_name,
        password_hash: roomData.password ? hashPassword(roomData.password) : null,
        has_password: !!roomData.password,
        is_active: true,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
        last_activity: new Date().toISOString(),
      })
      .select()
      .single();

    if (error) {
      console.error('Error creating room:', error);
      throw new Error(`Failed to create room: ${error.message}`);
    }

    return {
      id: data.id,
      host_id: data.host_id,
      host_name: data.host_name,
      password_hash: data.password_hash || undefined,
      has_password: data.has_password,
      is_active: data.is_active,
      created_at: data.created_at,
      updated_at: data.updated_at,
      last_activity: data.last_activity,
    };
  }

  // Get room data
  async getRoomData(roomId: string): Promise<RoomData | null> {
    const { data, error } = await supabase
      .from('rooms')
      .select('*')
      .eq('id', roomId)
      .eq('is_active', true)
      .single();

    if (error) {
      if (error.code === 'PGRST116') {
        return null; // Room not found
      }
      console.error('Error getting room data:', error);
      throw new Error(`Failed to get room data: ${error.message}`);
    }

    return {
      id: data.id,
      host_id: data.host_id,
      host_name: data.host_name,
      password_hash: data.password_hash || undefined,
      has_password: data.has_password,
      is_active: data.is_active,
      created_at: data.created_at,
      updated_at: data.updated_at,
      last_activity: data.last_activity,
    };
  }

  // Verify room password
  async verifyRoomPassword(roomId: string, password: string): Promise<boolean> {
    const roomData = await this.getRoomData(roomId);
    if (!roomData || !roomData.has_password || !roomData.password_hash) {
      return !roomData?.has_password; // Return true if no password required
    }

    return verifyPassword(password, roomData.password_hash);
  }

  // Add user to room
  async addUserToRoom(roomId: string, user: RealTimeUser): Promise<void> {
    const { error } = await supabase
      .from('room_users')
      .upsert({
        room_id: roomId,
        user_id: user.id,
        user_name: user.name,
        joined_at: new Date().toISOString(),
        last_seen: new Date().toISOString(),
        is_online: true,
      });

    if (error) {
      console.error('Error adding user to room:', error);
      throw new Error(`Failed to add user to room: ${error.message}`);
    }

    // Update room activity
    await this.updateRoomActivity(roomId);
  }

  // Remove user from room
  async removeUserFromRoom(roomId: string, userId: string): Promise<void> {
    const { error } = await supabase
      .from('room_users')
      .update({ is_online: false, last_seen: new Date().toISOString() })
      .eq('room_id', roomId)
      .eq('user_id', userId);

    if (error) {
      console.error('Error removing user from room:', error);
      throw new Error(`Failed to remove user from room: ${error.message}`);
    }

    // Update room activity
    await this.updateRoomActivity(roomId);
  }

  // Get room users
  async getRoomUsers(roomId: string): Promise<RealTimeUser[]> {
    const { data, error } = await supabase
      .from('room_users')
      .select('*')
      .eq('room_id', roomId)
      .eq('is_online', true)
      .order('joined_at', { ascending: true });

    if (error) {
      console.error('Error getting room users:', error);
      throw new Error(`Failed to get room users: ${error.message}`);
    }

    return data.map(user => ({
      id: user.user_id,
      name: user.user_name,
      joinedAt: new Date(user.joined_at),
    }));
  }

  // Send message to room
  async sendMessage(roomId: string, message: RealTimeMessage): Promise<void> {
    const { error } = await supabase
      .from('room_messages')
      .insert({
        room_id: roomId,
        user_id: message.userId,
        user_name: message.userName,
        message_type: message.type,
        message_data: message.data,
        created_at: new Date().toISOString(),
      });

    if (error) {
      console.error('Error sending message:', error);
      throw new Error(`Failed to send message: ${error.message}`);
    }

    // Update room activity
    await this.updateRoomActivity(roomId);
  }

  // Get messages since timestamp
  async getMessagesSince(roomId: string, since: string): Promise<RoomMessage[]> {
    const { data, error } = await supabase
      .from('room_messages')
      .select('*')
      .eq('room_id', roomId)
      .gt('created_at', since)
      .order('created_at', { ascending: true });

    if (error) {
      console.error('Error getting messages:', error);
      throw new Error(`Failed to get messages: ${error.message}`);
    }

    return data;
  }

  // Update room activity
  async updateRoomActivity(roomId: string): Promise<void> {
    const { error } = await supabase
      .from('rooms')
      .update({
        last_activity: new Date().toISOString(),
        updated_at: new Date().toISOString(),
      })
      .eq('id', roomId);

    if (error) {
      console.error('Error updating room activity:', error);
    }
  }

  // Close room (host only)
  async closeRoom(roomId: string, hostId: string): Promise<void> {
    const { error } = await supabase
      .from('rooms')
      .update({ is_active: false, updated_at: new Date().toISOString() })
      .eq('id', roomId)
      .eq('host_id', hostId);

    if (error) {
      console.error('Error closing room:', error);
      throw new Error(`Failed to close room: ${error.message}`);
    }

    // Mark all users as offline
    await supabase
      .from('room_users')
      .update({ is_online: false, last_seen: new Date().toISOString() })
      .eq('room_id', roomId);
  }

  // Subscribe to room changes
  subscribeToRoom(roomId: string, callbacks: {
    onUserJoin?: (user: RealTimeUser) => void;
    onUserLeave?: (userId: string) => void;
    onMessage?: (message: RealTimeMessage) => void;
  }) {
    // Subscribe to user changes
    const userSubscription = supabase
      .channel(`room-users-${roomId}`)
      .on(
        'postgres_changes',
        {
          event: '*',
          schema: 'public',
          table: 'room_users',
          filter: `room_id=eq.${roomId}`,
        },
        (payload) => {
          if (payload.eventType === 'INSERT' && payload.new.is_online) {
            callbacks.onUserJoin?.({
              id: payload.new.user_id,
              name: payload.new.user_name,
              joinedAt: new Date(payload.new.joined_at),
            });
          } else if (payload.eventType === 'UPDATE' && !payload.new.is_online) {
            callbacks.onUserLeave?.(payload.new.user_id);
          }
        }
      )
      .subscribe();

    // Subscribe to messages
    const messageSubscription = supabase
      .channel(`room-messages-${roomId}`)
      .on(
        'postgres_changes',
        {
          event: 'INSERT',
          schema: 'public',
          table: 'room_messages',
          filter: `room_id=eq.${roomId}`,
        },
        (payload) => {
          callbacks.onMessage?.({
            type: payload.new.message_type as any,
            userId: payload.new.user_id,
            userName: payload.new.user_name,
            timestamp: new Date(payload.new.created_at),
            data: payload.new.message_data,
          });
        }
      )
      .subscribe();

    return () => {
      userSubscription.unsubscribe();
      messageSubscription.unsubscribe();
    };
  }
}

export const supabaseService = new SupabaseService();
